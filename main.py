from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2P<PERSON><PERSON>R<PERSON>quest<PERSON>orm, OAuth2PasswordBearer
from pydantic import BaseModel, EmailStr
from typing import Optional
from contextlib import asynccontextmanager
import hashlib
from datetime import datetime, timedelta, timezone
import jwt
from manage_db import new_reg, init_db, get_user_by_email, store_refresh_token, get_refresh_token, invalidate_refresh_token, invalidate_all_user_tokens
import secrets

# JWT settings
SECRET_KEY = "YOUR_SECRET_KEY_HERE"  # Change this in production!
REFRESH_SECRET_KEY = "YOUR_REFRESH_SECRET_KEY_HERE"  # Different key for refresh tokens
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 15  # Short-lived access token
REFRESH_TOKEN_EXPIRE_DAYS = 7     # Long-lived refresh token

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="signin")

# Lifespan event handler
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    init_db()
    yield
    # Shutdown (if needed)

# Initialize FastAPI app
app = FastAPI(
    title="Athlix API",
    description="User registration and authentication API",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request validation
class SignupRequest(BaseModel):
    first_name: str
    last_name: str
    email: EmailStr
    password: str

class SignupResponse(BaseModel):
    message: str
    user_id: Optional[int] = None

# Additional Pydantic models
class Token(BaseModel):
    access_token: str
    token_type: str

class SigninResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    user_id: int
    expires_in: int  # Access token expiration in seconds

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class RefreshTokenResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

@app.get("/")
async def root():
    return {
        "message": "Welcome to Athlix API",
        "endpoints": {
            "POST /signup": "Register a new user (first_name, last_name, email, password)",
            "POST /signin": "Login user and get access + refresh tokens (username=email, password)",
            "POST /refresh": "Get new access token using refresh token",
            "POST /logout": "Logout and invalidate refresh token",
            "POST /logout-all": "Logout from all devices",
            "GET /docs": "API documentation (Swagger UI)"
        },
        "token_info": {
            "access_token_expires": f"{ACCESS_TOKEN_EXPIRE_MINUTES} minutes",
            "refresh_token_expires": f"{REFRESH_TOKEN_EXPIRE_DAYS} days"
        }
    }

@app.post("/signup", response_model=SignupResponse)
async def signup(user_data: SignupRequest):
    """
    Create a new user account
    """
    try:
        # Hash the password for security
        hashed_password = hashlib.sha256(user_data.password.encode()).hexdigest()

        # Register the user
        user_id = new_reg(
            user_data.first_name,
            user_data.last_name,
            user_data.email,
            hashed_password
        )

        if user_id:
            return SignupResponse(
                message="User registered successfully",
            )
        else:
            raise HTTPException(status_code=400, detail="Registration failed")

    except Exception as e:
        if "UNIQUE constraint failed" in str(e):
            raise HTTPException(status_code=400, detail="Email already exists")
        else:
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/signin", response_model=SigninResponse)
async def signin(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    Authenticate a user and return access and refresh tokens
    """
    # Get user from database
    user = get_user_by_email(form_data.username)  # Using email as username

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check password
    hashed_password = hashlib.sha256(form_data.password.encode()).hexdigest()
    if hashed_password != user[4]:  # Assuming password is at index 4
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user[3], "user_id": user[0]},  # Using email as subject
        expires_delta=access_token_expires
    )

    # Create refresh token
    refresh_token = create_refresh_token(user[0])  # Pass user ID

    return SigninResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        user_id=user[0],  # Assuming id is at index 0
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60  # Convert to seconds
    )

def create_access_token(data: dict, expires_delta: timedelta):
    """Create a new JWT access token"""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + expires_delta
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(user_id: int):
    """Create a new refresh token"""
    # Generate a secure random token
    token_data = {
        "user_id": user_id,
        "type": "refresh",
        "jti": secrets.token_urlsafe(32)  # Unique token ID
    }

    # Set expiration
    expires_delta = timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    expire = datetime.now(timezone.utc) + expires_delta
    token_data.update({"exp": expire})

    # Create JWT refresh token
    refresh_token = jwt.encode(token_data, REFRESH_SECRET_KEY, algorithm=ALGORITHM)

    # Store in database
    store_refresh_token(user_id, refresh_token, expire)

    return refresh_token

def verify_refresh_token(token: str):
    """Verify and decode refresh token"""
    try:
        payload = jwt.decode(token, REFRESH_SECRET_KEY, algorithms=[ALGORITHM])
        if payload.get("type") != "refresh":
            return None
        return payload
    except jwt.PyJWTError:
        return None

@app.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_access_token(refresh_request: RefreshTokenRequest):
    """
    Get a new access token using a refresh token
    """
    # Verify the refresh token
    payload = verify_refresh_token(refresh_request.refresh_token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

    # Check if token exists in database and is active
    db_token = get_refresh_token(refresh_request.refresh_token)
    if not db_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh token not found or inactive"
        )

    # Check if token is expired
    expires_at = datetime.fromisoformat(db_token[3])  # expires_at is at index 3
    if datetime.now(timezone.utc) > expires_at.replace(tzinfo=timezone.utc):
        # Invalidate expired token
        invalidate_refresh_token(refresh_request.refresh_token)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh token expired"
        )

    # Get user info - db_token[6] is email from the JOIN query
    user = get_user_by_email(db_token[6])  # Email from JOIN query

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )

    # Create new access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user[3], "user_id": user[0]},
        expires_delta=access_token_expires
    )

    return RefreshTokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )

@app.post("/logout")
async def logout(refresh_request: RefreshTokenRequest):
    """
    Logout user by invalidating refresh token
    """
    # Verify the refresh token
    payload = verify_refresh_token(refresh_request.refresh_token)
    if payload:
        # Invalidate the specific refresh token
        invalidate_refresh_token(refresh_request.refresh_token)

    return {"message": "Logged out successfully"}

@app.post("/logout-all")
async def logout_all(refresh_request: RefreshTokenRequest):
    """
    Logout user from all devices by invalidating all refresh tokens
    """
    # Verify the refresh token
    payload = verify_refresh_token(refresh_request.refresh_token)
    if payload:
        user_id = payload["user_id"]
        # Invalidate all refresh tokens for this user
        invalidate_all_user_tokens(user_id)

    return {"message": "Logged out from all devices successfully"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="localhost", port=8002, reload=True)
    
