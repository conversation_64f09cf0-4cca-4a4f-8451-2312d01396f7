import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from jinja2 import Template
from config import settings
import secrets
from datetime import datetime, timedelta
from database.database import async_session_maker
from database.models import EmailVerificationToken, PasswordResetToken
from sqlalchemy import select

async def send_email(to_email: str, subject: str, html_content: str, text_content: str = None):
    """Send email using SMTP"""
    try:
        message = MIMEMultipart("alternative")
        message["Subject"] = subject
        message["From"] = f"{settings.smtp_from_name} <{settings.smtp_from_email}>"
        message["To"] = to_email

        # Add text part if provided
        if text_content:
            text_part = MIMEText(text_content, "plain")
            message.attach(text_part)

        # Add HTML part
        html_part = MIMEText(html_content, "html")
        message.attach(html_part)

        # Send email
        await aiosmtplib.send(
            message,
            hostname=settings.smtp_host,
            port=settings.smtp_port,
            start_tls=True,
            username=settings.smtp_username,
            password=settings.smtp_password,
        )
        
        print(f"Email sent successfully to {to_email}")
        return True
    except Exception as e:
        print(f"Failed to send email to {to_email}: {str(e)}")
        return False

async def create_verification_token(user_id: int) -> str:
    """Create and store email verification token"""
    token = secrets.token_urlsafe(32)
    expires_at = datetime.utcnow() + timedelta(hours=24)  # 24 hours expiry
    
    async with async_session_maker() as session:
        # Invalidate existing tokens
        existing_tokens = await session.execute(
            select(EmailVerificationToken).where(
                EmailVerificationToken.user_id == user_id,
                EmailVerificationToken.is_used == False
            )
        )
        for existing_token in existing_tokens.scalars():
            existing_token.is_used = True
        
        # Create new token
        verification_token = EmailVerificationToken(
            user_id=user_id,
            token=token,
            expires_at=expires_at
        )
        session.add(verification_token)
        await session.commit()
    
    return token

async def create_password_reset_token(user_id: int) -> str:
    """Create and store password reset token"""
    token = secrets.token_urlsafe(32)
    expires_at = datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry
    
    async with async_session_maker() as session:
        # Invalidate existing tokens
        existing_tokens = await session.execute(
            select(PasswordResetToken).where(
                PasswordResetToken.user_id == user_id,
                PasswordResetToken.is_used == False
            )
        )
        for existing_token in existing_tokens.scalars():
            existing_token.is_used = True
        
        # Create new token
        reset_token = PasswordResetToken(
            user_id=user_id,
            token=token,
            expires_at=expires_at
        )
        session.add(reset_token)
        await session.commit()
    
    return token

async def send_verification_email(email: str, user_id: int, token: str = None):
    """Send email verification email"""
    if not token:
        token = await create_verification_token(user_id)
    
    verification_url = f"{settings.frontend_url}/verify-email?token={token}"
    
    html_template = Template("""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Verify Your Email - ATHLIX</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #2c3e50;">Welcome to ATHLIX!</h1>
            <p>Thank you for registering with ATHLIX. Please verify your email address to complete your registration.</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ verification_url }}" 
                   style="background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                   Verify Email Address
                </a>
            </div>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #7f8c8d;">{{ verification_url }}</p>
            <p><strong>This link will expire in 24 hours.</strong></p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            <p style="color: #7f8c8d; font-size: 12px;">
                If you didn't create an account with ATHLIX, you can safely ignore this email.
            </p>
        </div>
    </body>
    </html>
    """)
    
    html_content = html_template.render(verification_url=verification_url)
    text_content = f"""
    Welcome to ATHLIX!
    
    Thank you for registering. Please verify your email address by visiting:
    {verification_url}
    
    This link will expire in 24 hours.
    
    If you didn't create an account with ATHLIX, you can safely ignore this email.
    """
    
    await send_email(
        to_email=email,
        subject="Verify Your Email - ATHLIX",
        html_content=html_content,
        text_content=text_content
    )

async def send_password_reset_email(email: str, token: str):
    """Send password reset email"""
    reset_url = f"{settings.frontend_url}/reset-password?token={token}"
    
    html_template = Template("""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Reset Your Password - ATHLIX</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #2c3e50;">Reset Your Password</h1>
            <p>You requested to reset your password for your ATHLIX account.</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ reset_url }}" 
                   style="background-color: #e74c3c; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                   Reset Password
                </a>
            </div>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #7f8c8d;">{{ reset_url }}</p>
            <p><strong>This link will expire in 1 hour.</strong></p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            <p style="color: #7f8c8d; font-size: 12px;">
                If you didn't request a password reset, you can safely ignore this email.
            </p>
        </div>
    </body>
    </html>
    """)
    
    html_content = html_template.render(reset_url=reset_url)
    text_content = f"""
    Reset Your Password - ATHLIX
    
    You requested to reset your password. Click the link below to reset it:
    {reset_url}
    
    This link will expire in 1 hour.
    
    If you didn't request a password reset, you can safely ignore this email.
    """
    
    await send_email(
        to_email=email,
        subject="Reset Your Password - ATHLIX",
        html_content=html_content,
        text_content=text_content
    )
