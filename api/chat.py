from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, status
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc
from database.database import get_async_session
from database.models import User, Chat<PERSON>ession, ChatMessage, TreatmentSession
from auth.auth import current_active_user
from schemas.chat import (
    ChatSessionCreate, ChatSessionResponse, ChatSessionUpdate,
    ChatMessageCreate, ChatMessageResponse, ChatHistoryResponse,
    TreatmentSessionCreate, TreatmentSessionResponse,
    ImageUploadResponse, TreatmentResponse, QuestionResponse
)
from typing import List, Optional, AsyncGenerator
import uuid
import json
import asyncio
from datetime import datetime
import base64
import io
from PIL import Image
from openai import OpenAI
from config import settings
import os

router = APIRouter(prefix="/chat", tags=["chat"])

# OpenAI client
client = OpenAI(api_key=settings.openai_api_key)

@router.post("/sessions", response_model=ChatSessionResponse)
async def create_chat_session(
    session_data: ChatSessionCreate,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Create a new chat session"""
    session_id = str(uuid.uuid4())
    
    chat_session = ChatSession(
        user_id=current_user.id,
        session_id=session_id,
        title=session_data.title or f"Chat {datetime.now().strftime('%Y-%m-%d %H:%M')}",
        session_type=session_data.session_type,
        metadata=session_data.metadata
    )
    
    db.add(chat_session)
    await db.commit()
    await db.refresh(chat_session)
    
    return ChatSessionResponse(
        id=chat_session.id,
        session_id=chat_session.session_id,
        user_id=chat_session.user_id,
        title=chat_session.title,
        session_type=chat_session.session_type,
        created_at=chat_session.created_at,
        updated_at=chat_session.updated_at,
        is_active=chat_session.is_active,
        metadata=chat_session.metadata,
        message_count=0
    )

@router.get("/sessions", response_model=List[ChatSessionResponse])
async def get_user_chat_sessions(
    skip: int = 0,
    limit: int = 50,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Get user's chat sessions"""
    # Get sessions with message count
    query = select(
        ChatSession,
        func.count(ChatMessage.id).label("message_count")
    ).outerjoin(
        ChatMessage, ChatSession.id == ChatMessage.session_id
    ).where(
        ChatSession.user_id == current_user.id,
        ChatSession.is_active == True
    ).group_by(
        ChatSession.id
    ).order_by(
        desc(ChatSession.updated_at)
    ).offset(skip).limit(limit)
    
    result = await db.execute(query)
    sessions_with_counts = result.all()
    
    return [
        ChatSessionResponse(
            id=session.id,
            session_id=session.session_id,
            user_id=session.user_id,
            title=session.title,
            session_type=session.session_type,
            created_at=session.created_at,
            updated_at=session.updated_at,
            is_active=session.is_active,
            metadata=session.metadata,
            message_count=message_count
        )
        for session, message_count in sessions_with_counts
    ]

@router.get("/sessions/{session_id}", response_model=ChatHistoryResponse)
async def get_chat_history(
    session_id: str,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Get chat history for a specific session"""
    # Get session
    session_query = select(ChatSession).where(
        ChatSession.session_id == session_id,
        ChatSession.user_id == current_user.id
    )
    session_result = await db.execute(session_query)
    session = session_result.scalar_one_or_none()
    
    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    # Get messages
    messages_query = select(ChatMessage).where(
        ChatMessage.session_id == session.id
    ).order_by(ChatMessage.created_at)
    
    messages_result = await db.execute(messages_query)
    messages = messages_result.scalars().all()
    
    return ChatHistoryResponse(
        session=ChatSessionResponse(
            id=session.id,
            session_id=session.session_id,
            user_id=session.user_id,
            title=session.title,
            session_type=session.session_type,
            created_at=session.created_at,
            updated_at=session.updated_at,
            is_active=session.is_active,
            metadata=session.metadata,
            message_count=len(messages)
        ),
        messages=[
            ChatMessageResponse(
                id=msg.id,
                message_id=msg.message_id,
                session_id=session.session_id,
                role=msg.role,
                content=msg.content,
                message_type=msg.message_type,
                created_at=msg.created_at,
                metadata=msg.metadata
            )
            for msg in messages
        ]
    )

@router.put("/sessions/{session_id}", response_model=ChatSessionResponse)
async def update_chat_session(
    session_id: str,
    session_update: ChatSessionUpdate,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Update chat session"""
    query = select(ChatSession).where(
        ChatSession.session_id == session_id,
        ChatSession.user_id == current_user.id
    )
    result = await db.execute(query)
    session = result.scalar_one_or_none()
    
    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    # Update fields
    if session_update.title is not None:
        session.title = session_update.title
    if session_update.is_active is not None:
        session.is_active = session_update.is_active
    if session_update.metadata is not None:
        session.metadata = session_update.metadata
    
    session.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(session)
    
    return ChatSessionResponse(
        id=session.id,
        session_id=session.session_id,
        user_id=session.user_id,
        title=session.title,
        session_type=session.session_type,
        created_at=session.created_at,
        updated_at=session.updated_at,
        is_active=session.is_active,
        metadata=session.metadata
    )

@router.delete("/sessions/{session_id}")
async def delete_chat_session(
    session_id: str,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Delete chat session (soft delete)"""
    query = select(ChatSession).where(
        ChatSession.session_id == session_id,
        ChatSession.user_id == current_user.id
    )
    result = await db.execute(query)
    session = result.scalar_one_or_none()
    
    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    session.is_active = False
    session.updated_at = datetime.utcnow()
    
    await db.commit()
    
    return {"message": "Chat session deleted successfully"}

async def add_message_to_session(
    db: AsyncSession,
    session_db_id: int,
    role: str,
    content: str,
    message_type: str = "text",
    metadata: dict = None
) -> ChatMessage:
    """Add a message to chat session"""
    message = ChatMessage(
        session_id=session_db_id,
        message_id=str(uuid.uuid4()),
        role=role,
        content=content,
        message_type=message_type,
        metadata=metadata
    )

    db.add(message)
    await db.commit()
    await db.refresh(message)

    return message

@router.post("/sessions/{session_id}/messages", response_model=ChatMessageResponse)
async def send_message(
    session_id: str,
    message_data: ChatMessageCreate,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Send a message to chat session"""
    # Get session
    session_query = select(ChatSession).where(
        ChatSession.session_id == session_id,
        ChatSession.user_id == current_user.id,
        ChatSession.is_active == True
    )
    session_result = await db.execute(session_query)
    session = session_result.scalar_one_or_none()

    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # Add user message
    user_message = await add_message_to_session(
        db, session.id, "user", message_data.content, message_data.message_type
    )

    # Update session timestamp
    session.updated_at = datetime.utcnow()
    await db.commit()

    return ChatMessageResponse(
        id=user_message.id,
        message_id=user_message.message_id,
        session_id=session_id,
        role=user_message.role,
        content=user_message.content,
        message_type=user_message.message_type,
        created_at=user_message.created_at,
        metadata=user_message.metadata
    )

async def generate_chat_response(chat_history: List[dict], message: str) -> AsyncGenerator[str, None]:
    """Generate streaming chat response"""
    try:
        messages = [
            {
                "role": "system",
                "content": """You are ATHLIX, an AI Treatment Assistant specializing in injury prevention, treatment recommendations, and health guidance.

Key guidelines:
- Be empathetic, supportive, and professional
- Provide specific, actionable medical advice when appropriate
- Ask relevant follow-up questions to understand symptoms better
- Focus on injury prevention, exercise safety, and general wellness
- Encourage users to upload images for better analysis when relevant
- Be conversational and natural in your responses
- Always prioritize user safety and recommend professional medical care when necessary

When users describe symptoms, provide:
1. Immediate care recommendations
2. Questions about location, severity, duration
3. Suggestions for when to seek professional help
4. Prevention tips for the future

Format your responses clearly and maintain a caring, professional tone while being highly informative and actionable."""
            }
        ]

        # Add chat history (last 10 messages for context)
        for msg in chat_history[-10:]:
            messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })

        # Add current message
        messages.append({
            "role": "user",
            "content": message
        })

        # Stream response from OpenAI
        stream = client.chat.completions.create(
            model="gpt-4",
            messages=messages,
            max_tokens=1200,
            temperature=0.7,
            stream=True
        )

        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content
                await asyncio.sleep(0.01)

    except Exception as e:
        yield f"I apologize, but I encountered an error: {str(e)}. Please try again."

@router.get("/sessions/{session_id}/stream")
async def stream_chat_response(
    session_id: str,
    message: str,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Stream AI response for chat"""
    # Get session and verify ownership
    session_query = select(ChatSession).where(
        ChatSession.session_id == session_id,
        ChatSession.user_id == current_user.id,
        ChatSession.is_active == True
    )
    session_result = await db.execute(session_query)
    session = session_result.scalar_one_or_none()

    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # Get chat history
    messages_query = select(ChatMessage).where(
        ChatMessage.session_id == session.id
    ).order_by(ChatMessage.created_at)

    messages_result = await db.execute(messages_query)
    messages = messages_result.scalars().all()

    chat_history = [
        {"role": msg.role, "content": msg.content}
        for msg in messages
    ]

    async def generate():
        full_response = ""

        async for chunk in generate_chat_response(chat_history, message):
            full_response += chunk
            yield f"data: {json.dumps({'chunk': chunk, 'done': False})}\n\n"

        # Add AI response to database
        await add_message_to_session(
            db, session.id, "assistant", full_response
        )

        # Update session timestamp
        session.updated_at = datetime.utcnow()
        await db.commit()

        # Send completion signal
        yield f"data: {json.dumps({'chunk': '', 'done': True})}\n\n"

    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain",
        }
    )

def encode_image_from_upload(upload_file: UploadFile) -> str:
    """Convert uploaded file to base64 string"""
    try:
        # Read the file content
        image_content = upload_file.file.read()

        # Validate it's an image
        try:
            img = Image.open(io.BytesIO(image_content))
            img.verify()
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid image file")

        # Convert to base64
        return base64.b64encode(image_content).decode("utf-8")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing image: {str(e)}")

def get_initial_questions_from_image(base64_image: str) -> List[dict]:
    """Send image to OpenAI and get initial questions"""
    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": """You are a medical assessment AI. Analyze the provided image and generate important questions to gather more information for treatment diagnosis.

                    Return your response as a JSON object with this exact structure:
                    {
                        "questions": [
                            {
                                "id": "q1",
                                "question": "Your question here",
                                "type": "text|multiple_choice|number",
                                "options": ["option1", "option2"] // only for multiple_choice
                            }
                        ]
                    }

                    Generate 3-5 relevant questions based on what you see in the image. Focus on symptoms, pain levels, duration, activities that trigger issues, and medical history relevant to what's shown."""
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Analyze this image and provide important questions for medical assessment."},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                        }
                    ]
                }
            ],
            max_tokens=1000
        )

        # Parse the JSON response
        response_text = response.choices[0].message.content
        questions_data = json.loads(response_text)
        return questions_data.get("questions", [])

    except json.JSONDecodeError:
        # Fallback if JSON parsing fails
        return [
            {
                "id": "q1",
                "question": "Can you describe the pain or discomfort you're experiencing?",
                "type": "text"
            },
            {
                "id": "q2",
                "question": "On a scale of 1-10, how would you rate your pain level?",
                "type": "number"
            },
            {
                "id": "q3",
                "question": "How long have you been experiencing this issue?",
                "type": "multiple_choice",
                "options": ["Less than a week", "1-2 weeks", "1 month", "Several months", "Over a year"]
            }
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting questions from AI: {str(e)}")

@router.post("/treatment/upload-image", response_model=ImageUploadResponse)
async def upload_image_for_treatment(
    file: UploadFile = File(...),
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Upload image and get initial questions for treatment"""

    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")

    # Check file size
    if file.size > settings.max_file_size:
        raise HTTPException(status_code=400, detail="File too large")

    try:
        # Process image
        base64_image = encode_image_from_upload(file)

        # Get initial questions
        questions = get_initial_questions_from_image(base64_image)

        # Create treatment session
        session_id = str(uuid.uuid4())
        treatment_session = TreatmentSession(
            user_id=current_user.id,
            session_id=session_id,
            image_data=base64_image,
            questions=questions,
            answers=[],
            status="questioning"
        )

        db.add(treatment_session)
        await db.commit()

        return ImageUploadResponse(
            session_id=session_id,
            status="questioning",
            questions=questions,
            current_question=questions[0] if questions else None
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing upload: {str(e)}")

def generate_treatment_recommendation(session_data: dict) -> dict:
    """Generate treatment recommendation based on collected data"""
    try:
        # Prepare conversation context
        conversation_context = f"""
        Image Analysis: User uploaded an image for medical assessment.

        Questions and Answers:
        """

        for qa in session_data.get("answers", []):
            conversation_context += f"Q: {qa['question']}\nA: {qa['answer']}\n\n"

        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": """You are a certified personal trainer and injury prevention expert. Be friendly, clear, and cautious.

Based on the conversation history, provide comprehensive treatment recommendations following this EXACT structure:

• Immediate Treatment: [Provide immediate first aid and treatment steps]

• Injury Prevention Tips: [Provide specific prevention strategies]

• Overall Performance Evaluation: [Evaluate how the person handled the situation]

• Suggestions for Improving Performance Safely: [Provide safety improvement suggestions]

• Motivational and Positive Feedback: [Give encouraging and positive feedback]

• Future Workouts Recommendations: [Provide specific workout and recovery recommendations]

Return ONLY a JSON object in this format:
{
    "treatment": "Your detailed treatment recommendations here with the exact structure above"
}

Make the treatment recommendations comprehensive, specific, and actionable. Focus on practical advice that the person can implement immediately."""
                },
                {
                    "role": "user",
                    "content": conversation_context
                }
            ],
            max_tokens=2000
        )

        response_text = response.choices[0].message.content

        # Try to parse as JSON first
        try:
            parsed_response = json.loads(response_text)
            return parsed_response
        except json.JSONDecodeError:
            # If not JSON, assume it's a treatment response
            return {
                "treatment": response_text
            }

    except Exception as e:
        # Fallback treatment if there's an error
        fallback_treatment = """
        • Immediate Treatment: Clean the affected area with lukewarm water and mild soap. Apply gentle pressure to stop any bleeding and cover with a clean bandage.

        • Injury Prevention Tips: Always wear appropriate safety equipment when engaging in physical activities. Be aware of your surroundings and avoid risky situations when possible.

        • Overall Performance Evaluation: You've done well by seeking medical guidance. Taking prompt action shows good health awareness.

        • Suggestions for Improving Performance Safely: Consider warming up properly before activities and cooling down afterward. Stay hydrated and listen to your body's signals.

        • Motivational and Positive Feedback: You're taking the right steps by getting proper guidance. Your proactive approach to health and safety is commendable.

        • Future Workouts Recommendations: Start with low-impact exercises and gradually increase intensity. Focus on proper form over speed or weight. Consider consulting with a fitness professional for a personalized plan.
        """

        return {
            "treatment": fallback_treatment
        }

@router.post("/treatment/{session_id}/answer")
async def answer_treatment_question(
    session_id: str,
    response: QuestionResponse,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Submit answer to a treatment question"""

    # Get treatment session
    query = select(TreatmentSession).where(
        TreatmentSession.session_id == session_id,
        TreatmentSession.user_id == current_user.id
    )
    result = await db.execute(query)
    session = result.scalar_one_or_none()

    if not session:
        raise HTTPException(status_code=404, detail="Treatment session not found")

    if session.status != "questioning":
        raise HTTPException(status_code=400, detail="Session is not in questioning state")

    # Find the question being answered
    questions = session.questions or []
    question_found = None

    for q in questions:
        if q["id"] == response.question_id:
            question_found = q
            break

    if not question_found:
        raise HTTPException(status_code=400, detail="Question not found")

    # Add to answers
    answers = session.answers or []
    answers.append({
        "question_id": response.question_id,
        "question": question_found["question"],
        "answer": response.answer
    })
    session.answers = answers

    # Check if all questions are answered
    if len(answers) >= len(questions):
        # Generate treatment recommendation
        ai_response = generate_treatment_recommendation({
            "answers": answers,
            "questions": questions
        })

        treatment = ai_response.get("treatment", "No treatment recommendations available")
        session.treatment_recommendation = treatment
        session.status = "completed"

        await db.commit()

        return TreatmentResponse(
            session_id=session_id,
            treatment=treatment,
            session_complete=True
        )
    else:
        # More questions to answer
        next_question_index = len(answers)
        next_question = questions[next_question_index] if next_question_index < len(questions) else None

        await db.commit()

        return {
            "session_id": session_id,
            "status": "questioning",
            "current_question": next_question,
            "questions_remaining": len(questions) - len(answers)
        }

@router.get("/treatment/{session_id}", response_model=TreatmentSessionResponse)
async def get_treatment_session(
    session_id: str,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Get treatment session details"""

    query = select(TreatmentSession).where(
        TreatmentSession.session_id == session_id,
        TreatmentSession.user_id == current_user.id
    )
    result = await db.execute(query)
    session = result.scalar_one_or_none()

    if not session:
        raise HTTPException(status_code=404, detail="Treatment session not found")

    return TreatmentSessionResponse(
        id=session.id,
        session_id=session.session_id,
        user_id=session.user_id,
        status=session.status,
        questions=session.questions,
        answers=session.answers,
        treatment_recommendation=session.treatment_recommendation,
        created_at=session.created_at,
        updated_at=session.updated_at
    )
