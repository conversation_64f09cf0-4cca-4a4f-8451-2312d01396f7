#!/usr/bin/env python3
"""
Comprehensive test script for ATHLIX backend
Tests all major functionality including authentication, chat, and API endpoints
"""

import asyncio
import httpx
import json
import time
from datetime import datetime
import subprocess
import sys
import os

BASE_URL = "http://localhost:8000"
TEST_USER_EMAIL = f"test_{int(time.time())}@athlix.com"
TEST_USER_PASSWORD = "testpassword123"

class AthlixTester:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.access_token = None
        self.user_id = None
        self.session_id = None
        
    async def test_basic_endpoints(self):
        """Test basic API endpoints"""
        print("🔍 Testing basic endpoints...")
        
        # Test root endpoint
        response = await self.client.get(f"{BASE_URL}/")
        assert response.status_code == 200, f"Root endpoint failed: {response.status_code}"
        data = response.json()
        assert "message" in data, "Root endpoint missing message"
        print("✅ Root endpoint working")
        
        # Test health endpoint
        response = await self.client.get(f"{BASE_URL}/health")
        assert response.status_code == 200, f"Health endpoint failed: {response.status_code}"
        data = response.json()
        assert data["status"] == "healthy", "Health check failed"
        print("✅ Health endpoint working")
        
        # Test docs endpoint
        response = await self.client.get(f"{BASE_URL}/docs")
        assert response.status_code == 200, f"Docs endpoint failed: {response.status_code}"
        print("✅ API documentation accessible")
        
    async def test_user_registration(self):
        """Test user registration"""
        print("🔍 Testing user registration...")
        
        user_data = {
            "first_name": "Test",
            "last_name": "User",
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }
        
        response = await self.client.post(f"{BASE_URL}/auth/register", json=user_data)
        
        if response.status_code == 201:
            print("✅ User registration successful")
            data = response.json()
            self.user_id = data.get("id")
            return True
        else:
            print(f"⚠️  User registration returned {response.status_code}: {response.text}")
            return False
    
    async def test_user_login(self):
        """Test user login"""
        print("🔍 Testing user login...")
        
        login_data = {
            "username": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }
        
        response = await self.client.post(
            f"{BASE_URL}/auth/jwt/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            data = response.json()
            self.access_token = data.get("access_token")
            print("✅ User login successful")
            return True
        else:
            print(f"⚠️  User login failed: {response.status_code} - {response.text}")
            return False
    
    async def test_protected_endpoints(self):
        """Test protected endpoints with authentication"""
        if not self.access_token:
            print("⚠️  Skipping protected endpoint tests - no access token")
            return False
            
        print("🔍 Testing protected endpoints...")
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        # Test user profile endpoint
        response = await self.client.get(f"{BASE_URL}/users/me", headers=headers)
        if response.status_code == 200:
            print("✅ User profile endpoint working")
            return True
        else:
            print(f"⚠️  User profile endpoint failed: {response.status_code}")
            return False
    
    async def test_chat_endpoints(self):
        """Test chat functionality"""
        if not self.access_token:
            print("⚠️  Skipping chat tests - no access token")
            return False
            
        print("🔍 Testing chat endpoints...")
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        # Create chat session
        session_data = {
            "title": "Test Chat Session",
            "session_type": "chat"
        }
        
        response = await self.client.post(
            f"{BASE_URL}/chat/sessions",
            json=session_data,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            self.session_id = data.get("session_id")
            print("✅ Chat session creation working")
            
            # Test getting chat sessions
            response = await self.client.get(f"{BASE_URL}/chat/sessions", headers=headers)
            if response.status_code == 200:
                print("✅ Chat session listing working")
                return True
            else:
                print(f"⚠️  Chat session listing failed: {response.status_code}")
                return False
        else:
            print(f"⚠️  Chat session creation failed: {response.status_code}")
            return False
    
    async def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting ATHLIX Backend Tests")
        print("=" * 50)
        
        tests = [
            ("Basic Endpoints", self.test_basic_endpoints),
            ("User Registration", self.test_user_registration),
            ("User Login", self.test_user_login),
            ("Protected Endpoints", self.test_protected_endpoints),
            ("Chat Endpoints", self.test_chat_endpoints),
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name} test...")
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} test failed with error: {e}")
                results.append((test_name, False))
        
        # Print summary
        print("\n📊 Test Results Summary")
        print("=" * 30)
        passed = 0
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 Tests Passed: {passed}/{len(tests)}")
        
        if passed == len(tests):
            print("🎉 All tests passed! ATHLIX backend is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
        
        await self.client.aclose()
        return passed == len(tests)

async def check_server_running():
    """Check if server is running"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health", timeout=5.0)
            return response.status_code == 200
    except:
        return False

async def main():
    """Main test function"""
    print("🔍 Checking if ATHLIX server is running...")
    
    if not await check_server_running():
        print("❌ ATHLIX server is not running!")
        print("💡 Please start the server first:")
        print("   python simple_test.py")
        print("   OR")
        print("   python main_app.py")
        return False
    
    print("✅ Server is running, starting tests...")
    
    tester = AthlixTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 ATHLIX Backend Test Suite: ALL TESTS PASSED!")
        print("\n🔗 Your backend is ready for:")
        print("   - User registration and authentication")
        print("   - Protected API endpoints")
        print("   - Chat functionality")
        print("   - API documentation at http://localhost:8000/docs")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration.")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test runner error: {e}")
        sys.exit(1)
