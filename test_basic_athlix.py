#!/usr/bin/env python3
"""
Comprehensive test script for Basic ATHLIX backend
Tests all functionality including user management, chat, and treatment features
"""

import asyncio
import httpx
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"
TEST_USER_EMAIL = f"test_{int(time.time())}@athlix.com"
TEST_USER_PASSWORD = "testpassword123"

class BasicAthlixTester:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.user_id = None
        self.access_token = None
        self.chat_session_id = None
        self.treatment_session_id = None
        
    async def test_basic_endpoints(self):
        """Test basic API endpoints"""
        print("🔍 Testing basic endpoints...")
        
        # Test root endpoint
        response = await self.client.get(f"{BASE_URL}/")
        assert response.status_code == 200, f"Root endpoint failed: {response.status_code}"
        data = response.json()
        assert "message" in data, "Root endpoint missing message"
        assert "ATHLIX" in data["message"], "Root endpoint missing ATHLIX branding"
        print("✅ Root endpoint working")
        
        # Test health endpoint
        response = await self.client.get(f"{BASE_URL}/health")
        assert response.status_code == 200, f"Health endpoint failed: {response.status_code}"
        data = response.json()
        assert data["status"] == "healthy", "Health check failed"
        print("✅ Health endpoint working")
        
        # Test docs endpoint
        response = await self.client.get(f"{BASE_URL}/docs")
        assert response.status_code == 200, f"Docs endpoint failed: {response.status_code}"
        print("✅ API documentation accessible")
        
        return True
        
    async def test_user_registration(self):
        """Test user registration"""
        print("🔍 Testing user registration...")
        
        user_data = {
            "first_name": "Test",
            "last_name": "User",
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }
        
        response = await self.client.post(f"{BASE_URL}/register", json=user_data)
        
        if response.status_code == 200:
            data = response.json()
            self.user_id = data.get("id")
            assert self.user_id, "User ID not returned"
            assert data["email"] == TEST_USER_EMAIL, "Email mismatch"
            print("✅ User registration successful")
            return True
        else:
            print(f"❌ User registration failed: {response.status_code} - {response.text}")
            return False
    
    async def test_user_login(self):
        """Test user login"""
        print("🔍 Testing user login...")
        
        login_data = {
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }
        
        response = await self.client.post(f"{BASE_URL}/login", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            self.access_token = data.get("access_token")
            assert self.access_token, "Access token not returned"
            assert data["user"]["email"] == TEST_USER_EMAIL, "User email mismatch"
            print("✅ User login successful")
            return True
        else:
            print(f"❌ User login failed: {response.status_code} - {response.text}")
            return False
    
    async def test_user_listing(self):
        """Test user listing"""
        print("🔍 Testing user listing...")
        
        response = await self.client.get(f"{BASE_URL}/users")
        
        if response.status_code == 200:
            data = response.json()
            assert "users" in data, "Users list not returned"
            assert data["total"] >= 1, "No users found"
            print(f"✅ User listing working (found {data['total']} users)")
            return True
        else:
            print(f"❌ User listing failed: {response.status_code}")
            return False
    
    async def test_chat_functionality(self):
        """Test chat functionality"""
        print("🔍 Testing chat functionality...")
        
        # Create chat session
        response = await self.client.post(f"{BASE_URL}/chat/new")
        if response.status_code == 200:
            data = response.json()
            self.chat_session_id = data.get("session_id")
            assert self.chat_session_id, "Chat session ID not returned"
            print("✅ Chat session creation working")
        else:
            print(f"❌ Chat session creation failed: {response.status_code}")
            return False
        
        # Send chat message
        message_data = {
            "session_id": self.chat_session_id,
            "message": "Hello ATHLIX, I need help with my health.",
            "message_type": "text"
        }
        
        response = await self.client.post(f"{BASE_URL}/chat/message", json=message_data)
        if response.status_code == 200:
            data = response.json()
            assert data["session_id"] == self.chat_session_id, "Session ID mismatch"
            assert "response" in data, "AI response not returned"
            print("✅ Chat messaging working")
        else:
            print(f"❌ Chat messaging failed: {response.status_code}")
            return False
        
        # Get chat history
        response = await self.client.get(f"{BASE_URL}/chat/{self.chat_session_id}/history")
        if response.status_code == 200:
            data = response.json()
            assert data["session_id"] == self.chat_session_id, "Session ID mismatch"
            assert len(data["messages"]) >= 2, "Chat history incomplete"  # User + AI message
            print("✅ Chat history retrieval working")
        else:
            print(f"❌ Chat history failed: {response.status_code}")
            return False
        
        # List chat sessions
        response = await self.client.get(f"{BASE_URL}/chat/sessions")
        if response.status_code == 200:
            data = response.json()
            assert "sessions" in data, "Sessions list not returned"
            assert data["total"] >= 1, "No chat sessions found"
            print("✅ Chat session listing working")
            return True
        else:
            print(f"❌ Chat session listing failed: {response.status_code}")
            return False
    
    async def test_treatment_functionality(self):
        """Test treatment functionality"""
        print("🔍 Testing treatment functionality...")
        
        # Start treatment session
        response = await self.client.post(f"{BASE_URL}/treatment/start")
        if response.status_code == 200:
            data = response.json()
            self.treatment_session_id = data.get("session_id")
            assert self.treatment_session_id, "Treatment session ID not returned"
            assert data["status"] == "questioning", "Treatment status incorrect"
            assert "current_question" in data, "Current question not returned"
            print("✅ Treatment session creation working")
        else:
            print(f"❌ Treatment session creation failed: {response.status_code}")
            return False
        
        # Answer treatment questions
        questions_answered = 0
        current_question_id = "q1"
        
        for i, answer_text in enumerate(["I have pain in my knee", "7", "1-2 weeks"]):
            answer_data = {
                "session_id": self.treatment_session_id,
                "question_id": f"q{i+1}",
                "answer": answer_text
            }
            
            response = await self.client.post(f"{BASE_URL}/treatment/answer", json=answer_data)
            if response.status_code == 200:
                data = response.json()
                questions_answered += 1
                if data["status"] == "completed":
                    assert "treatment" in data, "Treatment recommendation not returned"
                    print("✅ Treatment completion working")
                    break
                else:
                    assert "current_question" in data, "Next question not returned"
            else:
                print(f"❌ Treatment answer failed: {response.status_code}")
                return False
        
        # Get treatment session status
        response = await self.client.get(f"{BASE_URL}/treatment/{self.treatment_session_id}")
        if response.status_code == 200:
            data = response.json()
            assert data["session_id"] == self.treatment_session_id, "Session ID mismatch"
            assert len(data["answers"]) == questions_answered, "Answer count mismatch"
            print("✅ Treatment session retrieval working")
            return True
        else:
            print(f"❌ Treatment session retrieval failed: {response.status_code}")
            return False
    
    async def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Basic ATHLIX Backend Tests")
        print("=" * 50)
        
        tests = [
            ("Basic Endpoints", self.test_basic_endpoints),
            ("User Registration", self.test_user_registration),
            ("User Login", self.test_user_login),
            ("User Listing", self.test_user_listing),
            ("Chat Functionality", self.test_chat_functionality),
            ("Treatment Functionality", self.test_treatment_functionality),
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name} test...")
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} test failed with error: {e}")
                results.append((test_name, False))
        
        # Print summary
        print("\n📊 Test Results Summary")
        print("=" * 30)
        passed = 0
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 Tests Passed: {passed}/{len(tests)}")
        
        if passed == len(tests):
            print("🎉 All tests passed! Basic ATHLIX backend is working perfectly.")
            print("\n🔗 Your backend supports:")
            print("   ✅ User registration and authentication")
            print("   ✅ Chat sessions with AI responses")
            print("   ✅ Treatment questionnaires")
            print("   ✅ Health monitoring")
            print("   ✅ API documentation")
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
        
        await self.client.aclose()
        return passed == len(tests)

async def check_server_running():
    """Check if server is running"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health", timeout=5.0)
            return response.status_code == 200
    except:
        return False

async def main():
    """Main test function"""
    print("🔍 Checking if Basic ATHLIX server is running...")
    
    if not await check_server_running():
        print("❌ Basic ATHLIX server is not running!")
        print("💡 Please start the server first:")
        print("   python basic_athlix.py")
        return False
    
    print("✅ Server is running, starting comprehensive tests...")
    
    tester = BasicAthlixTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 BASIC ATHLIX BACKEND TEST SUITE: ALL TESTS PASSED!")
        print("\n📋 What was tested:")
        print("   🔐 User registration and login system")
        print("   💬 Chat functionality with AI responses")
        print("   🏥 Treatment questionnaire system")
        print("   📊 Health monitoring and session management")
        print("   📚 API documentation and endpoints")
        print("\n🌐 Access your application:")
        print("   - API: http://localhost:8000")
        print("   - Docs: http://localhost:8000/docs")
        print("   - Health: http://localhost:8000/health")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration.")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Test runner error: {e}")
        exit(1)
