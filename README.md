# ATHLIX - AI Treatment Assistant

A comprehensive web application backend for AI-powered medical treatment assistance with advanced user management, chat functionality, and image-based medical assessment.

## 🌟 Features

### Authentication & User Management
- **JWT Authentication** with Access & Refresh Tokens
- **Email Verification** system
- **Password Reset** functionality
- **User Profile Management**
- **Account Security** with token management

### Chat System
- **Persistent Chat History** per user
- **Real-time Streaming** AI responses
- **Session Management** with CRUD operations
- **Multiple Chat Types** (general chat, image analysis, treatment)

### Medical Assessment
- **Image Upload** for medical analysis
- **Interactive Questionnaire** system
- **AI-Powered Treatment Recommendations**
- **Comprehensive Assessment Reports**

### Technical Features
- **FastAPI** with async/await support
- **SQLAlchemy** with async database operations
- **Alembic** database migrations
- **Email System** with HTML templates
- **CORS** support for frontend integration
- **Comprehensive API Documentation**

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- OpenAI API key
- SMTP email service (Gmail, etc.)

### Installation

1. **Clone and setup:**
```bash
git clone <your-repo>
cd athlix
python setup.py
```

2. **Configure environment:**
```bash
# Copy and edit the .env file
cp .env.example .env
# Edit .env with your API keys and settings
```

3. **Run the application:**
```bash
python main_app.py
```

4. **Access the API:**
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

## 📁 Project Structure

```
athlix/
├── main_app.py              # Main FastAPI application
├── config.py                # Configuration settings
├── requirements.txt         # Python dependencies
├── setup.py                 # Setup script
├── alembic.ini             # Database migration config
├── alembic/                # Database migrations
│   ├── env.py
│   └── script.py.mako
├── database/               # Database models and connection
│   ├── models.py           # SQLAlchemy models
│   └── database.py         # Database setup
├── auth/                   # Authentication system
│   ├── auth.py             # FastAPI-Users setup
│   └── email.py            # Email verification/reset
├── api/                    # API endpoints
│   ├── chat.py             # Chat and treatment endpoints
│   └── users.py            # User management endpoints
└── schemas/                # Pydantic models
    ├── user.py             # User schemas
    └── chat.py             # Chat schemas
```

## 🔧 Configuration

### Environment Variables (.env)

```env
# Application
SECRET_KEY=your-secret-key
DEBUG=True

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Database
DATABASE_URL=sqlite:///./athlix.db

# Email (Gmail example)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=ATHLIX

# Frontend
FRONTEND_URL=http://localhost:3000
```

## 📚 API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/jwt/login` - Login and get tokens
- `POST /auth/jwt/logout` - Logout
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password
- `POST /auth/request-verify-token` - Request email verification
- `POST /auth/verify` - Verify email

### User Management
- `GET /users/me` - Get current user profile
- `PUT /users/me` - Update user profile
- `DELETE /users/me` - Delete user account
- `POST /users/request-verification` - Request email verification
- `POST /users/verify-email` - Verify email
- `POST /users/request-password-reset` - Request password reset
- `POST /users/reset-password` - Reset password

### Chat Management
- `POST /chat/sessions` - Create new chat session
- `GET /chat/sessions` - Get user's chat sessions
- `GET /chat/sessions/{session_id}` - Get chat history
- `PUT /chat/sessions/{session_id}` - Update chat session
- `DELETE /chat/sessions/{session_id}` - Delete chat session
- `POST /chat/sessions/{session_id}/messages` - Send message
- `GET /chat/sessions/{session_id}/stream` - Stream AI response

### Treatment System
- `POST /chat/treatment/upload-image` - Upload image for analysis
- `POST /chat/treatment/{session_id}/answer` - Answer treatment question
- `GET /chat/treatment/{session_id}` - Get treatment session details

## 🔐 Security Features

- **JWT Tokens** with configurable expiration
- **Password Hashing** with bcrypt
- **Email Verification** required for sensitive operations
- **Token Invalidation** on logout
- **CORS Protection** with configurable origins
- **Input Validation** with Pydantic
- **SQL Injection Protection** with SQLAlchemy

## 📧 Email System

The application includes a comprehensive email system for:
- **User Registration** verification
- **Password Reset** requests
- **HTML Email Templates** with responsive design
- **SMTP Configuration** for various providers

## 🗄️ Database Schema

### Users Table
- User authentication and profile information
- Email verification status
- Account preferences and settings

### Chat Sessions
- Persistent chat history per user
- Session metadata and organization
- Message threading and timestamps

### Treatment Sessions
- Image-based medical assessments
- Question-answer tracking
- Treatment recommendations

### Token Management
- Refresh token storage and validation
- Email verification tokens
- Password reset tokens

## 🚀 Deployment

### Development
```bash
python main_app.py
```

### Production
```bash
# Using Gunicorn
pip install gunicorn
gunicorn main_app:app -w 4 -k uvicorn.workers.UvicornWorker

# Using Docker (create Dockerfile)
docker build -t athlix .
docker run -p 8000:8000 athlix
```

## 🧪 Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest
```

## 📖 API Documentation

Once the application is running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Review the health check at `/health`
- Check application logs for debugging

## 🔄 Migration from Legacy Code

If migrating from the original `chat.py`, the new system provides:
- **Enhanced Security** with proper authentication
- **Persistent Storage** instead of in-memory sessions
- **User Management** with profiles and preferences
- **Email Integration** for verification and recovery
- **Scalable Architecture** with proper separation of concerns
