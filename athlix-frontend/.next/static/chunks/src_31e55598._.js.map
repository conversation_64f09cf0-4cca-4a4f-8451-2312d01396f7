{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function formatTime(dateString: string): string {\n  const date = new Date(dateString);\n  return date.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function getInitials(firstName: string, lastName: string): string {\n  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function isValidSessionId(sessionId: string): boolean {\n  // UUID v4 regex\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(sessionId);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,YAAY,SAAiB,EAAE,QAAgB;IAC7D,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;AAClE;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,iBAAiB,SAAiB;IAChD,gBAAgB;IAChB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', loading = false, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background';\n    \n    const variants = {\n      default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n      outline: 'border border-input hover:bg-accent hover:text-accent-foreground',\n      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n      ghost: 'hover:bg-accent hover:text-accent-foreground',\n      link: 'underline-offset-4 hover:underline text-primary',\n    };\n\n    const sizes = {\n      default: 'h-10 py-2 px-4',\n      sm: 'h-9 px-3 rounded-md',\n      lg: 'h-11 px-8 rounded-md',\n      icon: 'h-10 w-10',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpG,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/Button';\nimport { \n  HeartIcon, \n  UserIcon, \n  ChatBubbleLeftIcon, \n  ClipboardDocumentListIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ArrowRightOnRectangleIcon\n} from '@heroicons/react/24/outline';\nimport { getInitials } from '@/lib/utils';\n\nexport function Navbar() {\n  const { user, logout, isAuthenticated } = useAuth();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', icon: HeartIcon },\n    { name: 'Chat', href: '/chat', icon: ChatBubbleLeftIcon },\n    { name: 'Treatment', href: '/treatment', icon: ClipboardDocumentListIcon },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and brand */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg\">\n                <HeartIcon className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">ATHLIX</span>\n            </Link>\n          </div>\n\n          {/* Desktop navigation */}\n          {isAuthenticated && (\n            <div className=\"hidden md:flex items-center space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center space-x-1 text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  <item.icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              ))}\n            </div>\n          )}\n\n          {/* User menu */}\n          <div className=\"flex items-center space-x-4\">\n            {isAuthenticated ? (\n              <div className=\"flex items-center space-x-3\">\n                {/* User avatar */}\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-blue-600\">\n                      {user ? getInitials(user.first_name, user.last_name) : 'U'}\n                    </span>\n                  </div>\n                  <span className=\"hidden sm:block text-sm text-gray-700\">\n                    {user?.first_name} {user?.last_name}\n                  </span>\n                </div>\n                \n                {/* Logout button */}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={logout}\n                  className=\"text-gray-600 hover:text-gray-900\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"w-4 h-4\" />\n                  <span className=\"hidden sm:ml-2 sm:block\">Logout</span>\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Link href=\"/login\">\n                  <Button variant=\"ghost\" size=\"sm\">\n                    Login\n                  </Button>\n                </Link>\n                <Link href=\"/register\">\n                  <Button size=\"sm\">\n                    Sign Up\n                  </Button>\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            {isAuthenticated && (\n              <div className=\"md:hidden\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n                >\n                  {mobileMenuOpen ? (\n                    <XMarkIcon className=\"w-5 h-5\" />\n                  ) : (\n                    <Bars3Icon className=\"w-5 h-5\" />\n                  )}\n                </Button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isAuthenticated && mobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 border-t\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                <item.icon className=\"w-5 h-5\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAfA;;;;;;;AAiBO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,oNAAA,CAAA,YAAS;QAAC;QACzD;YAAE,MAAM;YAAQ,MAAM;YAAS,MAAM,sOAAA,CAAA,qBAAkB;QAAC;QACxD;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,oPAAA,CAAA,4BAAyB;QAAC;KAC1E;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;wBAKrD,iCACC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAYtB,6LAAC;4BAAI,WAAU;;gCACZ,gCACC,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,OAAO,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS,IAAI;;;;;;;;;;;8DAG3D,6LAAC;oDAAK,WAAU;;wDACb,MAAM;wDAAW;wDAAE,MAAM;;;;;;;;;;;;;sDAK9B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,oPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;8DACrC,6LAAC;oDAAK,WAAU;8DAA0B;;;;;;;;;;;;;;;;;yDAI9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAAK;;;;;;;;;;;sDAIpC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;0DAAK;;;;;;;;;;;;;;;;;gCAQvB,iCACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,kBAAkB,CAAC;kDAEjC,+BACC,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAErB,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlC,mBAAmB,gCAClB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACrB,6LAAC;8CAAM,KAAK,IAAI;;;;;;;2BANX,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAc9B;GA1HgB;;QAC4B,kIAAA,CAAA,UAAO;;;KADnC", "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/athlix/athlix-frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Navbar } from '@/components/layout/Navbar';\nimport { Button } from '@/components/ui/Button';\nimport { \n  HeartIcon, \n  ChatBubbleLeftIcon, \n  ClipboardDocumentListIcon,\n  ShieldCheckIcon,\n  SparklesIcon,\n  UserGroupIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Home() {\n  const { isAuthenticated } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  const features = [\n    {\n      name: 'AI-Powered Chat',\n      description: 'Get instant health advice and treatment recommendations from our advanced AI assistant.',\n      icon: ChatBubbleLeftIcon,\n    },\n    {\n      name: 'Treatment Assessment',\n      description: 'Upload images and answer questions to receive personalized treatment plans.',\n      icon: ClipboardDocumentListIcon,\n    },\n    {\n      name: 'Injury Prevention',\n      description: 'Learn how to prevent injuries and maintain optimal health with expert guidance.',\n      icon: ShieldCheckIcon,\n    },\n    {\n      name: 'Personalized Care',\n      description: 'Receive customized health recommendations based on your specific needs.',\n      icon: SparklesIcon,\n    },\n    {\n      name: 'Expert Knowledge',\n      description: 'Access medical knowledge from certified professionals and trusted sources.',\n      icon: UserGroupIcon,\n    },\n    {\n      name: 'Health Monitoring',\n      description: 'Track your health journey and monitor progress over time.',\n      icon: HeartIcon,\n    },\n  ];\n\n  if (isAuthenticated) {\n    return null; // Will redirect to dashboard\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navbar />\n      \n      {/* Hero Section */}\n      <div className=\"relative overflow-hidden\">\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n          <div className=\"relative z-10 pb-8 sm:pb-16 md:pb-20 lg:w-full lg:max-w-2xl lg:pb-28 xl:pb-32\">\n            <main className=\"mx-auto mt-10 max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28\">\n              <div className=\"sm:text-center lg:text-left\">\n                <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl\">\n                  <span className=\"block xl:inline\">Your AI-Powered</span>{' '}\n                  <span className=\"block text-blue-600 xl:inline\">Health Assistant</span>\n                </h1>\n                <p className=\"mt-3 text-base text-gray-500 sm:mx-auto sm:mt-5 sm:max-w-xl sm:text-lg md:mt-5 md:text-xl lg:mx-0\">\n                  Get instant health advice, treatment recommendations, and injury prevention tips from ATHLIX, \n                  your personal AI treatment assistant. Available 24/7 to help you stay healthy and active.\n                </p>\n                <div className=\"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start\">\n                  <div className=\"rounded-md shadow\">\n                    <Link href=\"/register\">\n                      <Button size=\"lg\" className=\"w-full\">\n                        Get Started Free\n                      </Button>\n                    </Link>\n                  </div>\n                  <div className=\"mt-3 sm:ml-3 sm:mt-0\">\n                    <Link href=\"/login\">\n                      <Button variant=\"outline\" size=\"lg\" className=\"w-full\">\n                        Sign In\n                      </Button>\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </main>\n          </div>\n        </div>\n        <div className=\"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2\">\n          <div className=\"h-56 w-full bg-gradient-to-br from-blue-50 to-blue-100 sm:h-72 md:h-96 lg:h-full lg:w-full flex items-center justify-center\">\n            <div className=\"text-center\">\n              <HeartIcon className=\"mx-auto h-24 w-24 text-blue-600\" />\n              <p className=\"mt-4 text-lg font-medium text-blue-600\">ATHLIX AI Assistant</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Features Section */}\n      <div className=\"py-12 bg-gray-50\">\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n          <div className=\"lg:text-center\">\n            <h2 className=\"text-base font-semibold uppercase tracking-wide text-blue-600\">Features</h2>\n            <p className=\"mt-2 text-3xl font-bold leading-8 tracking-tight text-gray-900 sm:text-4xl\">\n              Everything you need for better health\n            </p>\n            <p className=\"mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto\">\n              ATHLIX combines advanced AI technology with medical expertise to provide comprehensive health assistance.\n            </p>\n          </div>\n\n          <div className=\"mt-10\">\n            <dl className=\"space-y-10 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10 md:space-y-0 lg:grid-cols-3\">\n              {features.map((feature) => (\n                <div key={feature.name} className=\"relative\">\n                  <dt>\n                    <div className=\"absolute flex h-12 w-12 items-center justify-center rounded-md bg-blue-500 text-white\">\n                      <feature.icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                    </div>\n                    <p className=\"ml-16 text-lg font-medium leading-6 text-gray-900\">{feature.name}</p>\n                  </dt>\n                  <dd className=\"mt-2 ml-16 text-base text-gray-500\">{feature.description}</dd>\n                </div>\n              ))}\n            </dl>\n          </div>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <div className=\"bg-blue-600\">\n        <div className=\"mx-auto max-w-2xl py-16 px-4 text-center sm:py-20 sm:px-6 lg:px-8\">\n          <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n            <span className=\"block\">Ready to improve your health?</span>\n            <span className=\"block\">Start your journey with ATHLIX today.</span>\n          </h2>\n          <p className=\"mt-4 text-lg leading-6 text-blue-200\">\n            Join thousands of users who trust ATHLIX for their health and wellness needs.\n          </p>\n          <Link href=\"/register\">\n            <Button size=\"lg\" variant=\"secondary\" className=\"mt-8\">\n              Create Your Account\n            </Button>\n          </Link>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"bg-white\">\n        <div className=\"mx-auto max-w-7xl py-12 px-4 sm:px-6 md:flex md:items-center md:justify-between lg:px-8\">\n          <div className=\"flex justify-center space-x-6 md:order-2\">\n            <p className=\"text-center text-xs leading-5 text-gray-500\">\n              &copy; 2024 ATHLIX. All rights reserved.\n            </p>\n          </div>\n          <div className=\"mt-8 md:order-1 md:mt-0\">\n            <div className=\"flex items-center justify-center md:justify-start\">\n              <HeartIcon className=\"h-6 w-6 text-blue-600\" />\n              <span className=\"ml-2 text-xl font-bold text-gray-900\">ATHLIX</span>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,WAAW;QACf;YACE,MAAM;YACN,aAAa;YACb,MAAM,sOAAA,CAAA,qBAAkB;QAC1B;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,oPAAA,CAAA,4BAAyB;QACjC;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,gOAAA,CAAA,kBAAe;QACvB;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,0NAAA,CAAA,eAAY;QACpB;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,4NAAA,CAAA,gBAAa;QACrB;QACA;YACE,MAAM;YACN,aAAa;YACb,MAAM,oNAAA,CAAA,YAAS;QACjB;KACD;IAED,IAAI,iBAAiB;QACnB,OAAO,MAAM,6BAA6B;IAC5C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,SAAM;;;;;0BAGP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CACd,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAkB;;;;;;gDAAuB;8DACzD,6LAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;sDAElD,6LAAC;4CAAE,WAAU;sDAAoG;;;;;;sDAIjH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,WAAU;sEAAS;;;;;;;;;;;;;;;;8DAKzC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,WAAU;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUrE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAC9E,6LAAC;oCAAE,WAAU;8CAA6E;;;;;;8CAG1F,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAKjE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CACX,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAAuB,WAAU;;0DAChC,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,QAAQ,IAAI;4DAAC,WAAU;4DAAU,eAAY;;;;;;;;;;;kEAEhD,6LAAC;wDAAE,WAAU;kEAAqD,QAAQ,IAAI;;;;;;;;;;;;0DAEhF,6LAAC;gDAAG,WAAU;0DAAsC,QAAQ,WAAW;;;;;;;uCAP/D,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,6LAAC;oCAAK,WAAU;8CAAQ;;;;;;;;;;;;sCAE1B,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAGpD,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAQ;gCAAY,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;;;0BAQ7D,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA8C;;;;;;;;;;;sCAI7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAK,WAAU;kDAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrE;GAlKwB;;QACM,kIAAA,CAAA,UAAO;QACpB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}