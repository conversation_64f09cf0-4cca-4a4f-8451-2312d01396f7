#!/usr/bin/env python3
"""
Simple test to verify basic FastAPI setup without complex authentication
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Simple FastAPI app for testing
app = FastAPI(title="ATHLIX Test", version="1.0.0")

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "ATHLIX Backend is running!",
        "status": "healthy",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "app": "ATHLIX",
        "version": "1.0.0"
    }

@app.get("/test")
async def test_endpoint():
    return {
        "message": "Test endpoint working",
        "features": [
            "FastAPI backend",
            "CORS enabled",
            "Health checks",
            "Ready for authentication"
        ]
    }

if __name__ == "__main__":
    print("🚀 Starting ATHLIX Test Server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    uvicorn.run("simple_test:app", host="0.0.0.0", port=8000, reload=True)
