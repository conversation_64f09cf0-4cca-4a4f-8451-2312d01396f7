import sqlite3

DATABASE_NAME = "appdb.db"

def init_db():
    """Initialize the database and create tables if they don't exist"""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()

    # Create users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create refresh tokens table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS refresh_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token TEXT UNIQUE NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users (id)
        )
    ''')

    conn.commit()
    conn.close()

def new_reg(first_name, last_name, email, password):
    """Register a new user"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()

        # Insert new user
        cursor.execute('''
            INSERT INTO users (first_name, last_name, email, password)
            VALUES (?, ?, ?, ?)
        ''', (first_name, last_name, email, password))

        user_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return user_id

    except sqlite3.IntegrityError as e:
        conn.close()
        raise e
    except Exception as e:
        conn.close()
        raise e

def get_user_by_email(email):
    """Get user by email"""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM users WHERE email = ?', (email,))
    user = cursor.fetchone()

    conn.close()
    return user

def store_refresh_token(user_id, token, expires_at):
    """Store a refresh token in the database"""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()

    cursor.execute('''
        INSERT INTO refresh_tokens (user_id, token, expires_at)
        VALUES (?, ?, ?)
    ''', (user_id, token, expires_at))

    conn.commit()
    conn.close()

def get_refresh_token(token):
    """Get refresh token from database"""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()

    cursor.execute('''
        SELECT rt.*, u.email, u.id as user_id
        FROM refresh_tokens rt
        JOIN users u ON rt.user_id = u.id
        WHERE rt.token = ? AND rt.is_active = 1
    ''', (token,))

    result = cursor.fetchone()
    conn.close()
    return result

def invalidate_refresh_token(token):
    """Invalidate a refresh token"""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()

    cursor.execute('''
        UPDATE refresh_tokens
        SET is_active = 0
        WHERE token = ?
    ''', (token,))

    conn.commit()
    conn.close()

def invalidate_all_user_tokens(user_id):
    """Invalidate all refresh tokens for a user"""
    conn = sqlite3.connect(DATABASE_NAME)
    cursor = conn.cursor()

    cursor.execute('''
        UPDATE refresh_tokens
        SET is_active = 0
        WHERE user_id = ?
    ''', (user_id,))

    conn.commit()
    conn.close()