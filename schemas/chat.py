from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class ChatMessageCreate(BaseModel):
    session_id: Optional[str] = None
    content: str
    message_type: str = "text"  # text, image, question_response

class ChatMessageResponse(BaseModel):
    id: int
    message_id: str
    session_id: str
    role: str
    content: str
    message_type: str
    created_at: datetime
    metadata: Optional[Dict[str, Any]] = None

class ChatSessionCreate(BaseModel):
    title: Optional[str] = None
    session_type: str = "chat"  # chat, image_analysis, treatment
    metadata: Optional[Dict[str, Any]] = None

class ChatSessionResponse(BaseModel):
    id: int
    session_id: str
    user_id: int
    title: Optional[str] = None
    session_type: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_active: bool
    metadata: Optional[Dict[str, Any]] = None
    message_count: Optional[int] = 0

class ChatSessionUpdate(BaseModel):
    title: Optional[str] = None
    is_active: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None

class ChatHistoryResponse(BaseModel):
    session: ChatSessionResponse
    messages: List[ChatMessageResponse]

class StreamChatRequest(BaseModel):
    session_id: str
    message: str

class QuestionResponse(BaseModel):
    session_id: str
    question_id: str
    answer: str

class TreatmentSessionCreate(BaseModel):
    image_data: Optional[str] = None

class TreatmentSessionResponse(BaseModel):
    id: int
    session_id: str
    user_id: int
    status: str
    questions: Optional[List[Dict[str, Any]]] = None
    answers: Optional[List[Dict[str, Any]]] = None
    treatment_recommendation: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

class ImageUploadResponse(BaseModel):
    session_id: str
    status: str
    questions: List[Dict[str, Any]]
    current_question: Optional[Dict[str, Any]] = None

class TreatmentResponse(BaseModel):
    session_id: str
    treatment: str
    session_complete: bool
