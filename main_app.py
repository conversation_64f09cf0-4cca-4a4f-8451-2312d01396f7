from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from database.database import create_db_and_tables
from auth.auth import auth_backend, fastapi_users
from schemas.user import UserRead, UserCreate, UserUpdate
from api.chat import router as chat_router
from api.users import router as users_router
from config import settings

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await create_db_and_tables()
    print("Database tables created")
    yield
    # Shutdown (if needed)

# Initialize FastAPI app
app = FastAPI(
    title=settings.app_name,
    description="ATHLIX - AI Treatment Assistant with comprehensive chat and user management",
    version=settings.app_version,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include authentication routes
app.include_router(
    fastapi_users.get_auth_router(auth_backend),
    prefix="/auth/jwt",
    tags=["auth"],
)

app.include_router(
    fastapi_users.get_register_router(UserRead, UserCreate),
    prefix="/auth",
    tags=["auth"],
)

app.include_router(
    fastapi_users.get_reset_password_router(),
    prefix="/auth",
    tags=["auth"],
)

app.include_router(
    fastapi_users.get_verify_router(UserRead),
    prefix="/auth",
    tags=["auth"],
)

app.include_router(
    fastapi_users.get_users_router(UserRead, UserUpdate),
    prefix="/users",
    tags=["users"],
)

# Include custom API routes
app.include_router(chat_router)
app.include_router(users_router)

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": f"Welcome to {settings.app_name} API",
        "version": settings.app_version,
        "description": "AI Treatment Assistant with comprehensive chat and user management",
        "endpoints": {
            "Authentication": {
                "POST /auth/register": "Register a new user",
                "POST /auth/jwt/login": "Login and get access token",
                "POST /auth/jwt/logout": "Logout",
                "POST /auth/forgot-password": "Request password reset",
                "POST /auth/reset-password": "Reset password with token",
                "POST /auth/request-verify-token": "Request email verification",
                "POST /auth/verify": "Verify email with token"
            },
            "User Management": {
                "GET /users/me": "Get current user profile",
                "PUT /users/me": "Update current user profile",
                "DELETE /users/me": "Delete current user account",
                "POST /users/request-verification": "Request email verification",
                "POST /users/verify-email": "Verify email",
                "POST /users/request-password-reset": "Request password reset",
                "POST /users/reset-password": "Reset password"
            },
            "Chat Management": {
                "POST /chat/sessions": "Create new chat session",
                "GET /chat/sessions": "Get user's chat sessions",
                "GET /chat/sessions/{session_id}": "Get chat history",
                "PUT /chat/sessions/{session_id}": "Update chat session",
                "DELETE /chat/sessions/{session_id}": "Delete chat session",
                "POST /chat/sessions/{session_id}/messages": "Send message",
                "GET /chat/sessions/{session_id}/stream": "Stream AI response"
            },
            "Treatment": {
                "POST /chat/treatment/upload-image": "Upload image for treatment analysis",
                "POST /chat/treatment/{session_id}/answer": "Answer treatment question",
                "GET /chat/treatment/{session_id}": "Get treatment session details"
            }
        },
        "features": [
            "JWT Authentication with Access & Refresh Tokens",
            "Email Verification",
            "Password Reset",
            "Persistent Chat History",
            "Image-based Medical Assessment",
            "Streaming AI Responses",
            "User Profile Management",
            "Session Management"
        ],
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": "development" if settings.debug else "production"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main_app:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
