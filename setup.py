#!/usr/bin/env python3
"""
ATHLIX Setup Script
This script helps set up the ATHLIX application with all necessary dependencies and configurations.
"""

import os
import sys
import subprocess
import secrets
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return None

def create_env_file():
    """Create .env file from template if it doesn't exist"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return
    
    print("🔄 Creating .env file from template...")
    
    # Read template
    with open(env_example, 'r') as f:
        content = f.read()
    
    # Generate a secure secret key
    secret_key = secrets.token_urlsafe(32)
    content = content.replace('your-super-secret-key-change-in-production', secret_key)
    
    # Write .env file
    with open(env_file, 'w') as f:
        f.write(content)
    
    print("✅ .env file created successfully")
    print("⚠️  Please update the following in your .env file:")
    print("   - OPENAI_API_KEY: Add your OpenAI API key")
    print("   - SMTP settings: Configure email settings for verification/reset emails")

def install_dependencies():
    """Install Python dependencies"""
    if not run_command("pip install -r requirements.txt", "Installing Python dependencies"):
        return False
    return True

def setup_database():
    """Set up database with Alembic"""
    # Initialize Alembic if not already done
    if not Path("alembic/versions").exists():
        if not run_command("alembic init alembic", "Initializing Alembic"):
            return False
    
    # Create initial migration
    if not run_command("alembic revision --autogenerate -m 'Initial migration'", "Creating initial migration"):
        return False
    
    # Run migrations
    if not run_command("alembic upgrade head", "Running database migrations"):
        return False
    
    return True

def main():
    """Main setup function"""
    print("🚀 Setting up ATHLIX Application")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Create .env file
    create_env_file()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        print("❌ Failed to setup database")
        sys.exit(1)
    
    print("\n🎉 ATHLIX setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Update your .env file with:")
    print("   - OpenAI API key")
    print("   - Email SMTP settings")
    print("2. Run the application:")
    print("   python main_app.py")
    print("3. Visit http://localhost:8000/docs for API documentation")
    print("\n🔗 Useful endpoints:")
    print("   - API Docs: http://localhost:8000/docs")
    print("   - Health Check: http://localhost:8000/health")
    print("   - Root Info: http://localhost:8000/")

if __name__ == "__main__":
    main()
