from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Dict, Optional, Any, AsyncGenerator
import base64
import json
import uuid
from datetime import datetime
import os
from openai import OpenAI
from dotenv import load_dotenv
import io
from PIL import Image
import asyncio

load_dotenv()

app = FastAPI(title="AI Treatment Assistant", version="2.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# OpenAI client
client = OpenAI(api_key=os.getenv("gpt_api"))

# In-memory storage for sessions and chat history
sessions: Dict[str, Dict] = {}
chat_sessions: Dict[str, List[Dict]] = {}

# Pydantic models
class ChatMessage(BaseModel):
    session_id: Optional[str] = None
    message: str
    message_type: str = "text"  # text, image, question_response

class QuestionResponse(BaseModel):
    session_id: str
    question_id: str
    answer: str

class SessionStatus(BaseModel):
    session_id: str
    status: str
    questions: List[Dict] = []
    current_question_index: int = 0
    treatment: Optional[str] = None

class TreatmentResponse(BaseModel):
    session_id: str
    treatment: str
    session_complete: bool

class ChatResponse(BaseModel):
    session_id: str
    message: str
    message_type: str = "ai_response"
    has_question: bool = False
    question: Optional[Dict] = None
    session_complete: bool = False
    treatment: Optional[str] = None

def encode_image_from_upload(upload_file: UploadFile) -> str:
    """Convert uploaded file to base64 string"""
    try:
        # Read the file content
        image_content = upload_file.file.read()
        
        # Validate it's an image
        try:
            img = Image.open(io.BytesIO(image_content))
            img.verify()
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid image file")
        
        # Convert to base64
        return base64.b64encode(image_content).decode("utf-8")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing image: {str(e)}")

def get_initial_questions_from_image(base64_image: str) -> List[Dict]:
    """Send image to OpenAI and get initial questions"""
    try:
        response = client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[
                {
                    "role": "system",
                    "content": """You are a medical assessment AI. Analyze the provided image and generate important questions to gather more information for treatment diagnosis. 
                    
                    Return your response as a JSON object with this exact structure:
                    {
                        "questions": [
                            {
                                "id": "q1",
                                "question": "Your question here",
                                "type": "text|multiple_choice|number",
                                "options": ["option1", "option2"] // only for multiple_choice
                            }
                        ]
                    }
                    
                    Generate 3-5 relevant questions based on what you see in the image. Focus on symptoms, pain levels, duration, activities that trigger issues, and medical history relevant to what's shown."""
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Analyze this image and provide important questions for medical assessment."},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                        }
                    ]
                }
            ],
            max_tokens=1000
        )
        
        # Parse the JSON response
        response_text = response.choices[0].message.content
        questions_data = json.loads(response_text)
        return questions_data.get("questions", [])
        
    except json.JSONDecodeError:
        # Fallback if JSON parsing fails
        return [
            {
                "id": "q1",
                "question": "Can you describe the pain or discomfort you're experiencing?",
                "type": "text"
            },
            {
                "id": "q2", 
                "question": "On a scale of 1-10, how would you rate your pain level?",
                "type": "number"
            },
            {
                "id": "q3",
                "question": "How long have you been experiencing this issue?",
                "type": "multiple_choice",
                "options": ["Less than a week", "1-2 weeks", "1 month", "Several months", "Over a year"]
            }
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting questions from AI: {str(e)}")

def generate_treatment_or_more_questions(session_data: Dict) -> Dict:
    """Generate treatment or ask more questions based on collected data"""
    try:
        # Prepare conversation history
        conversation_context = f"""
        Image Analysis: User uploaded an image for medical assessment.
        
        Questions and Answers:
        """
        
        for qa in session_data.get("qa_history", []):
            conversation_context += f"Q: {qa['question']}\nA: {qa['answer']}\n\n"
        
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": """You are a certified personal trainer and injury prevention expert. Be friendly, clear, and cautious.

Based on the conversation history, determine if you have enough information to provide treatment recommendations.

IMPORTANT: Always provide complete treatment recommendations. Do NOT ask additional questions. 

Provide comprehensive treatment recommendations following this EXACT structure with proper sections:

• Immediate Treatment: [Provide immediate first aid and treatment steps]

• Injury Prevention Tips: [Provide specific prevention strategies]

• Overall Performance Evaluation: [Evaluate how the person handled the situation]

• Suggestions for Improving Performance Safely: [Provide safety improvement suggestions]

• Motivational and Positive Feedback: [Give encouraging and positive feedback]

• Future Workouts Recommendations: [Provide specific workout and recovery recommendations]

Return ONLY a JSON object in this format:
{
    "need_more_info": false,
    "treatment": "Your detailed treatment recommendations here with the exact structure above"
}

Make the treatment recommendations comprehensive, specific, and actionable. Focus on practical advice that the person can implement immediately."""
                },
                {
                    "role": "user",
                    "content": conversation_context
                }
            ],
            max_tokens=2000
        )
        
        response_text = response.choices[0].message.content
        
        # Try to parse as JSON first
        try:
            parsed_response = json.loads(response_text)
            # Ensure we always return treatment, never ask more questions
            if parsed_response.get("need_more_info", False):
                # Force treatment generation if AI tries to ask more questions
                return {
                    "need_more_info": False,
                    "treatment": parsed_response.get("treatment", "Based on the information provided, here are my treatment recommendations...")
                }
            return parsed_response
        except json.JSONDecodeError:
            # If not JSON, assume it's a treatment response
            return {
                "need_more_info": False,
                "treatment": response_text
            }
            
    except Exception as e:
        # Fallback treatment if there's an error
        fallback_treatment = """
        • Immediate Treatment: Clean the affected area with lukewarm water and mild soap. Apply gentle pressure to stop any bleeding and cover with a clean bandage.

        • Injury Prevention Tips: Always wear appropriate safety equipment when engaging in physical activities. Be aware of your surroundings and avoid risky situations when possible.

        • Overall Performance Evaluation: You've done well by seeking medical guidance. Taking prompt action shows good health awareness.

        • Suggestions for Improving Performance Safely: Consider warming up properly before activities and cooling down afterward. Stay hydrated and listen to your body's signals.

        • Motivational and Positive Feedback: You're taking the right steps by getting proper guidance. Your proactive approach to health and safety is commendable.

        • Future Workouts Recommendations: Start with low-impact exercises and gradually increase intensity. Focus on proper form over speed or weight. Consider consulting with a fitness professional for a personalized plan.
        """
        
        return {
            "need_more_info": False,
            "treatment": fallback_treatment
        }

async def generate_chat_response(chat_history: List[Dict], message: str) -> AsyncGenerator[str, None]:
    """Generate streaming chat response for general conversation"""
    try:
        # Prepare conversation history for OpenAI
        messages = [
            {
                "role": "system",
                "content": """You are an AI Treatment Assistant - a highly knowledgeable medical AI specializing in injury prevention, treatment recommendations, and health guidance.

Key guidelines:
- Be empathetic, supportive, and professional
- Provide specific, actionable medical advice when appropriate
- Ask relevant follow-up questions to understand symptoms better
- Focus on injury prevention, exercise safety, and general wellness
- Encourage users to upload images for better analysis when relevant
- Be conversational and natural in your responses
- Always prioritize user safety and recommend professional medical care when necessary

When users describe symptoms, provide:
1. Immediate care recommendations
2. Questions about location, severity, duration
3. Suggestions for when to seek professional help
4. Prevention tips for the future

Format your responses clearly with:
- Immediate recommendations first
- Follow-up questions
- When to seek professional help
- Prevention strategies

Maintain a caring, professional tone while being highly informative and actionable."""
            }
        ]
        
        # Add chat history
        for msg in chat_history[-10:]:  # Last 10 messages for context
            messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        
        # Add current message
        messages.append({
            "role": "user",
            "content": message
        })
        
        # Stream response from OpenAI
        stream = client.chat.completions.create(
            model="gpt-4",
            messages=messages,
            max_tokens=1200,
            temperature=0.7,
            stream=True
        )
        
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content
                await asyncio.sleep(0.01)  # Small delay for smooth streaming
                
    except Exception as e:
        yield f"I apologize, but I encountered an error: {str(e)}. Please try describing your symptoms again, and I'll do my best to help you."

def create_chat_session() -> str:
    """Create a new chat session"""
    session_id = str(uuid.uuid4())
    chat_sessions[session_id] = []
    return session_id

def add_message_to_chat(session_id: str, role: str, content: str):
    """Add a message to chat history"""
    if session_id not in chat_sessions:
        chat_sessions[session_id] = []
    
    chat_sessions[session_id].append({
        "role": role,
        "content": content,
        "timestamp": datetime.now().isoformat()
    })

@app.post("/upload-image/")
async def upload_image(file: UploadFile = File(...)):
    """Upload image and get initial questions"""
    
    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    # Create session
    session_id = str(uuid.uuid4())
    
    try:
        # Process image
        base64_image = encode_image_from_upload(file)
        
        # Get initial questions
        questions = get_initial_questions_from_image(base64_image)
        
        # Store session data
        sessions[session_id] = {
            "session_id": session_id,
            "image_data": base64_image,
            "questions": questions,
            "current_question_index": 0,
            "qa_history": [],
            "status": "questioning",
            "created_at": datetime.now().isoformat()
        }
        
        return {
            "session_id": session_id,
            "status": "questioning",
            "questions": questions,
            "current_question": questions[0] if questions else None
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing upload: {str(e)}")

@app.post("/answer-question/")
async def answer_question(response: QuestionResponse):
    """Submit answer to a question"""
    
    if response.session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = sessions[response.session_id]
    
    if session["status"] != "questioning":
        raise HTTPException(status_code=400, detail="Session is not in questioning state")
    
    # Find the question being answered
    current_questions = session["questions"]
    question_found = None
    
    for q in current_questions:
        if q["id"] == response.question_id:
            question_found = q
            break
    
    if not question_found:
        raise HTTPException(status_code=400, detail="Question not found")
    
    # Add to QA history
    session["qa_history"].append({
        "question_id": response.question_id,
        "question": question_found["question"],
        "answer": response.answer
    })
    
    # Move to next question or check if we need treatment
    session["current_question_index"] += 1
    
    if session["current_question_index"] >= len(current_questions):
        # All questions answered, check if we need more info or can provide treatment
        ai_response = generate_treatment_or_more_questions(session)
        
        if ai_response.get("need_more_info", False):
            # Add more questions
            new_questions = ai_response.get("questions", [])
            session["questions"].extend(new_questions)
            session["current_question_index"] = len(current_questions)  # Point to first new question
            
            return {
                "session_id": response.session_id,
                "status": "questioning",
                "current_question": new_questions[0] if new_questions else None,
                "questions_remaining": len(new_questions)
            }
        else:
            # Provide treatment
            treatment = ai_response.get("treatment", "No treatment recommendations available")
            session["treatment"] = treatment
            session["status"] = "completed"
            
            return TreatmentResponse(
                session_id=response.session_id,
                treatment=treatment,
                session_complete=True
            )
    else:
        # More questions in current set
        next_question = current_questions[session["current_question_index"]]
        return {
            "session_id": response.session_id,
            "status": "questioning",
            "current_question": next_question,
            "questions_remaining": len(current_questions) - session["current_question_index"]
        }

@app.post("/chat/")
async def chat_message(chat_msg: ChatMessage):
    """Handle general chat messages"""
    
    # Create session if none provided
    if not chat_msg.session_id:
        chat_msg.session_id = create_chat_session()
    
    # Add user message to chat history
    add_message_to_chat(chat_msg.session_id, "user", chat_msg.message)
    
    return {"session_id": chat_msg.session_id, "message": "Message received"}

@app.get("/chat/{session_id}/stream")
async def stream_chat_response(session_id: str, message: str):
    """Stream AI response for chat"""
    
    if session_id not in chat_sessions:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    async def generate():
        chat_history = chat_sessions[session_id]
        full_response = ""
        
        async for chunk in generate_chat_response(chat_history, message):
            full_response += chunk
            yield f"data: {json.dumps({'chunk': chunk, 'done': False})}\n\n"
        
        # Add AI response to chat history
        add_message_to_chat(session_id, "assistant", full_response)
        
        # Send completion signal
        yield f"data: {json.dumps({'chunk': '', 'done': True})}\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain",
        }
    )

@app.post("/chat/{session_id}/message")
async def send_chat_message(session_id: str, message: str):
    """Send a message and get immediate response (non-streaming)"""
    
    if session_id not in chat_sessions:
        chat_sessions[session_id] = []
    
    # Add user message
    add_message_to_chat(session_id, "user", message)
    
    # Generate AI response
    chat_history = chat_sessions[session_id]
    full_response = ""
    
    async for chunk in generate_chat_response(chat_history, message):
        full_response += chunk
    
    # Add AI response to chat history
    add_message_to_chat(session_id, "assistant", full_response)
    
    return {
        "session_id": session_id,
        "response": full_response,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/session/{session_id}")
async def get_session_status(session_id: str):
    """Get current session status"""
    
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = sessions[session_id]
    
    return SessionStatus(
        session_id=session_id,
        status=session["status"],
        questions=session["questions"],
        current_question_index=session["current_question_index"],
        treatment=session.get("treatment")
    )

@app.get("/session/{session_id}/treatment")
async def get_treatment(session_id: str):
    """Get treatment recommendations for completed session"""
    
    if session_id not in sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = sessions[session_id]
    
    if session["status"] != "completed":
        raise HTTPException(status_code=400, detail="Session not completed yet")
    
    return {
        "session_id": session_id,
        "treatment": session.get("treatment", "No treatment available"),
        "qa_history": session.get("qa_history", [])
    }

@app.get("/chat/{session_id}/history")
async def get_chat_history(session_id: str):
    """Get chat history for a session"""
    
    if session_id not in chat_sessions:
        return {"session_id": session_id, "messages": []}
    
    return {
        "session_id": session_id,
        "messages": chat_sessions[session_id]
    }

@app.delete("/session/{session_id}")
async def delete_session(session_id: str):
    """Delete a session"""
    
    if session_id in sessions:
        del sessions[session_id]
    
    if session_id in chat_sessions:
        del chat_sessions[session_id]
    
    return {"message": "Session deleted successfully"}

@app.post("/chat/new")
async def create_new_chat():
    """Create a new chat session"""
    session_id = create_chat_session()
    return {"session_id": session_id}

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "AI Treatment Assistant API", "status": "active", "version": "2.0.0"}

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "active_sessions": len(sessions),
        "active_chats": len(chat_sessions),
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)