#!/usr/bin/env python3
"""
Simple test script to verify ATHLIX application setup
"""

import asyncio
import httpx
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

async def test_health_check():
    """Test health check endpoint"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("✅ Health check passed")
                print(f"   Response: {response.json()}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False

async def test_root_endpoint():
    """Test root endpoint"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/")
            if response.status_code == 200:
                print("✅ Root endpoint accessible")
                data = response.json()
                print(f"   App: {data.get('message', 'Unknown')}")
                return True
            else:
                print(f"❌ Root endpoint failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Root endpoint error: {e}")
            return False

async def test_docs_endpoint():
    """Test API documentation endpoint"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/docs")
            if response.status_code == 200:
                print("✅ API documentation accessible")
                return True
            else:
                print(f"❌ API docs failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API docs error: {e}")
            return False

async def test_user_registration():
    """Test user registration endpoint"""
    async with httpx.AsyncClient() as client:
        try:
            test_user = {
                "first_name": "Test",
                "last_name": "User",
                "email": f"test_{datetime.now().timestamp()}@example.com",
                "password": "testpassword123"
            }
            
            response = await client.post(f"{BASE_URL}/auth/register", json=test_user)
            if response.status_code in [200, 201]:
                print("✅ User registration working")
                return True
            else:
                print(f"❌ User registration failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ User registration error: {e}")
            return False

async def run_tests():
    """Run all tests"""
    print("🧪 Running ATHLIX Application Tests")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health_check),
        ("Root Endpoint", test_root_endpoint),
        ("API Documentation", test_docs_endpoint),
        ("User Registration", test_user_registration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        result = await test_func()
        results.append((test_name, result))
    
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Tests Passed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("🎉 All tests passed! ATHLIX is ready to use.")
        print("\n🔗 Access your application:")
        print(f"   - API Docs: {BASE_URL}/docs")
        print(f"   - Health Check: {BASE_URL}/health")
        print(f"   - Root Info: {BASE_URL}/")
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure the application is running (python main_app.py)")
        print("   2. Check your .env file configuration")
        print("   3. Verify database setup (python setup.py)")

def main():
    """Main function"""
    try:
        asyncio.run(run_tests())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test runner error: {e}")

if __name__ == "__main__":
    main()
