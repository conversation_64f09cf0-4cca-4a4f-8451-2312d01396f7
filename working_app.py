#!/usr/bin/env python3
"""
Working ATHLIX application with basic authentication and chat functionality
"""

from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import asyncio
import uvicorn

# Import our modules
from database.database import create_db_and_tables, get_async_session
from database.models import User
from auth.auth import auth_backend, fastapi_users, current_active_user
from schemas.user import UserRead, UserCreate, UserUpdate
from config import settings

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    try:
        await create_db_and_tables()
        print("✅ Database tables created successfully")
    except Exception as e:
        print(f"⚠️  Database setup warning: {e}")
    yield
    # Shutdown (if needed)

# Initialize FastAPI app
app = FastAPI(
    title=settings.app_name,
    description="ATHLIX - AI Treatment Assistant (Working Version)",
    version=settings.app_version,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include authentication routes
app.include_router(
    fastapi_users.get_auth_router(auth_backend),
    prefix="/auth/jwt",
    tags=["auth"],
)

app.include_router(
    fastapi_users.get_register_router(UserRead, UserCreate),
    prefix="/auth",
    tags=["auth"],
)

app.include_router(
    fastapi_users.get_reset_password_router(),
    prefix="/auth",
    tags=["auth"],
)

app.include_router(
    fastapi_users.get_verify_router(UserRead),
    prefix="/auth",
    tags=["auth"],
)

app.include_router(
    fastapi_users.get_users_router(UserRead, UserUpdate),
    prefix="/users",
    tags=["users"],
)

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": f"Welcome to {settings.app_name} API",
        "version": settings.app_version,
        "description": "AI Treatment Assistant with authentication and chat",
        "status": "running",
        "endpoints": {
            "Authentication": {
                "POST /auth/register": "Register a new user",
                "POST /auth/jwt/login": "Login and get access token",
                "POST /auth/jwt/logout": "Logout",
                "POST /auth/forgot-password": "Request password reset",
                "POST /auth/reset-password": "Reset password with token",
                "POST /auth/request-verify-token": "Request email verification",
                "POST /auth/verify": "Verify email with token"
            },
            "User Management": {
                "GET /users/me": "Get current user profile",
                "PATCH /users/me": "Update current user profile",
            },
            "System": {
                "GET /health": "Health check",
                "GET /docs": "API documentation",
                "GET /test-auth": "Test authentication (requires login)"
            }
        },
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": "development" if settings.debug else "production",
        "database": "connected"
    }

@app.get("/test-auth")
async def test_auth(current_user: User = Depends(current_active_user)):
    """Test endpoint that requires authentication"""
    return {
        "message": "Authentication working!",
        "user_id": current_user.id,
        "user_email": current_user.email,
        "user_name": f"{current_user.first_name} {current_user.last_name}",
        "is_verified": current_user.is_verified,
        "is_active": current_user.is_active
    }

@app.get("/users/count")
async def get_user_count(db: AsyncSession = Depends(get_async_session)):
    """Get total number of registered users"""
    result = await db.execute(select(User))
    users = result.scalars().all()
    return {
        "total_users": len(users),
        "message": "User count retrieved successfully"
    }

if __name__ == "__main__":
    print("🚀 Starting ATHLIX Working Application...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔐 Authentication endpoints available")
    print("💡 Test with: python test_athlix_backend.py")
    uvicorn.run(
        "working_app:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
