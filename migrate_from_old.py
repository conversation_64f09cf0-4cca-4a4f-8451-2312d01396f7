#!/usr/bin/env python3
"""
Migration script to help transition from the old chat.py system to the new ATHLIX system
"""

import sqlite3
import asyncio
import json
from datetime import datetime
from database.database import async_session_maker
from database.models import User, ChatSession, ChatMessage
from passlib.context import CryptContext
import uuid

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def migrate_users_from_old_db():
    """Migrate users from old database format"""
    try:
        # Connect to old database
        old_conn = sqlite3.connect("appdb.db")  # Your old database
        old_cursor = old_conn.cursor()
        
        # Get users from old database
        old_cursor.execute("SELECT * FROM users")
        old_users = old_cursor.fetchall()
        
        if not old_users:
            print("No users found in old database")
            return
        
        async with async_session_maker() as session:
            migrated_count = 0
            
            for old_user in old_users:
                # Assuming old format: (id, first_name, last_name, email, password, created_at)
                try:
                    # Check if user already exists
                    existing_user = await session.execute(
                        select(User).where(User.email == old_user[3])
                    )
                    if existing_user.scalar_one_or_none():
                        print(f"User {old_user[3]} already exists, skipping...")
                        continue
                    
                    # Create new user
                    new_user = User(
                        first_name=old_user[1],
                        last_name=old_user[2],
                        email=old_user[3],
                        hashed_password=old_user[4],  # Assuming already hashed
                        is_active=True,
                        is_verified=False,  # Will need to verify again
                        is_superuser=False
                    )
                    
                    session.add(new_user)
                    migrated_count += 1
                    
                except Exception as e:
                    print(f"Error migrating user {old_user[3]}: {e}")
                    continue
            
            await session.commit()
            print(f"✅ Migrated {migrated_count} users successfully")
        
        old_conn.close()
        
    except Exception as e:
        print(f"❌ Error migrating users: {e}")

def create_sample_admin_user():
    """Create a sample admin user for testing"""
    async def _create_admin():
        async with async_session_maker() as session:
            # Check if admin already exists
            from sqlalchemy import select
            existing_admin = await session.execute(
                select(User).where(User.email == "<EMAIL>")
            )
            if existing_admin.scalar_one_or_none():
                print("Admin user already exists")
                return
            
            admin_user = User(
                first_name="Admin",
                last_name="User",
                email="<EMAIL>",
                hashed_password=pwd_context.hash("admin123"),
                is_active=True,
                is_verified=True,
                is_superuser=True
            )
            
            session.add(admin_user)
            await session.commit()
            print("✅ Created admin user: <EMAIL> / admin123")
    
    return asyncio.run(_create_admin())

def create_sample_data():
    """Create sample data for testing"""
    async def _create_sample():
        async with async_session_maker() as session:
            from sqlalchemy import select
            
            # Get or create a test user
            test_user_email = "<EMAIL>"
            user_result = await session.execute(
                select(User).where(User.email == test_user_email)
            )
            test_user = user_result.scalar_one_or_none()
            
            if not test_user:
                test_user = User(
                    first_name="Test",
                    last_name="User",
                    email=test_user_email,
                    hashed_password=pwd_context.hash("test123"),
                    is_active=True,
                    is_verified=True,
                    is_superuser=False
                )
                session.add(test_user)
                await session.commit()
                await session.refresh(test_user)
            
            # Create sample chat session
            session_id = str(uuid.uuid4())
            chat_session = ChatSession(
                user_id=test_user.id,
                session_id=session_id,
                title="Welcome Chat",
                session_type="chat",
                metadata={"sample": True}
            )
            session.add(chat_session)
            await session.commit()
            await session.refresh(chat_session)
            
            # Add sample messages
            messages = [
                {
                    "role": "user",
                    "content": "Hello, I'm new to ATHLIX. Can you help me understand how this works?",
                    "message_type": "text"
                },
                {
                    "role": "assistant",
                    "content": "Welcome to ATHLIX! I'm your AI Treatment Assistant. I can help you with injury prevention, treatment recommendations, and health guidance. You can ask me about symptoms, upload images for analysis, or just have a general health conversation. How can I assist you today?",
                    "message_type": "text"
                }
            ]
            
            for msg_data in messages:
                message = ChatMessage(
                    session_id=chat_session.id,
                    message_id=str(uuid.uuid4()),
                    role=msg_data["role"],
                    content=msg_data["content"],
                    message_type=msg_data["message_type"]
                )
                session.add(message)
            
            await session.commit()
            print(f"✅ Created sample data for user: {test_user_email}")
    
    return asyncio.run(_create_sample())

def main():
    """Main migration function"""
    print("🔄 ATHLIX Migration Tool")
    print("=" * 30)
    
    print("\n1. Creating admin user...")
    create_sample_admin_user()
    
    print("\n2. Creating sample data...")
    create_sample_data()
    
    print("\n3. Attempting to migrate users from old database...")
    asyncio.run(migrate_users_from_old_db())
    
    print("\n✅ Migration completed!")
    print("\n📋 What's been created:")
    print("   - Admin user: <EMAIL> / admin123")
    print("   - Test user: <EMAIL> / test123")
    print("   - Sample chat session with welcome messages")
    
    print("\n🔧 Next steps:")
    print("   1. Start the application: python main_app.py")
    print("   2. Test login with admin or test user")
    print("   3. Check API docs: http://localhost:8000/docs")
    print("   4. Users from old system will need to verify their emails")

if __name__ == "__main__":
    main()
